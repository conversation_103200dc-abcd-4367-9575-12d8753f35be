/**
 * 翻译缓存 Redux Slice
 * 用于缓存页面翻译结果，避免重复翻译相同内容
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit'

// 翻译缓存项的类型定义
export interface TranslationCacheItem {
  originalText: string    // 原文文本
  translatedText: string  // 翻译结果
  timestamp: number       // 缓存时间戳
}

// 翻译缓存状态类型定义
export interface TranslationCacheState {
  cache: Record<string, TranslationCacheItem>  // 使用原文作为key的缓存映射
  maxCacheSize: number                         // 最大缓存条目数
  cacheExpireTime: number                      // 缓存过期时间（毫秒）
}

// 初始状态
const initialState: TranslationCacheState = {
  cache: {},
  maxCacheSize: 1000,        // 最多缓存1000条翻译结果
  cacheExpireTime: 24 * 60 * 60 * 1000  // 24小时过期
}

// 创建翻译缓存slice
const translationCacheSlice = createSlice({
  name: 'translationCache',
  initialState,
  reducers: {
    // 存储翻译结果到缓存
    setTranslationCache: (
      state,
      action: PayloadAction<{ originalText: string; translatedText: string }>
    ) => {
      const { originalText, translatedText } = action.payload
      const now = Date.now()
      
      // 创建缓存key（使用原文的hash或直接使用原文）
      const cacheKey = originalText.trim()
      
      // 如果缓存已满，删除最旧的条目
      const cacheKeys = Object.keys(state.cache)
      if (cacheKeys.length >= state.maxCacheSize) {
        // 找到最旧的条目并删除
        let oldestKey = cacheKeys[0]
        let oldestTime = state.cache[oldestKey].timestamp
        
        for (const key of cacheKeys) {
          if (state.cache[key].timestamp < oldestTime) {
            oldestTime = state.cache[key].timestamp
            oldestKey = key
          }
        }
        
        delete state.cache[oldestKey]
      }
      
      // 存储新的翻译缓存
      state.cache[cacheKey] = {
        originalText,
        translatedText,
        timestamp: now
      }
    },

    // 从缓存获取翻译结果（通过reducer实现，但通常通过selector获取）
    getTranslationCache: (state, action: PayloadAction<string>) => {
      // 这个reducer主要用于触发状态更新，实际获取通过selector
      const originalText = action.payload.trim()
      const cacheItem = state.cache[originalText]
      
      if (cacheItem) {
        const now = Date.now()
        // 检查是否过期
        if (now - cacheItem.timestamp > state.cacheExpireTime) {
          // 过期则删除
          delete state.cache[originalText]
        }
      }
    },

    // 清除单个缓存条目
    removeTranslationCache: (state, action: PayloadAction<string>) => {
      const originalText = action.payload.trim()
      delete state.cache[originalText]
    },

    // 清除所有缓存
    clearAllTranslationCache: (state) => {
      state.cache = {}
    },

    // 清除过期的缓存条目
    clearExpiredCache: (state) => {
      const now = Date.now()
      const expiredKeys: string[] = []
      
      // 找出所有过期的key
      for (const [key, item] of Object.entries(state.cache)) {
        if (now - item.timestamp > state.cacheExpireTime) {
          expiredKeys.push(key)
        }
      }
      
      // 删除过期的条目
      expiredKeys.forEach(key => {
        delete state.cache[key]
      })
    },

    // 设置缓存配置
    setCacheConfig: (
      state,
      action: PayloadAction<{ maxCacheSize?: number; cacheExpireTime?: number }>
    ) => {
      const { maxCacheSize, cacheExpireTime } = action.payload
      
      if (maxCacheSize !== undefined) {
        state.maxCacheSize = maxCacheSize
      }
      
      if (cacheExpireTime !== undefined) {
        state.cacheExpireTime = cacheExpireTime
      }
    }
  }
})

// 导出actions
export const {
  setTranslationCache,
  getTranslationCache,
  removeTranslationCache,
  clearAllTranslationCache,
  clearExpiredCache,
  setCacheConfig
} = translationCacheSlice.actions

// 导出reducer
export default translationCacheSlice.reducer

// Selectors - 用于从状态中获取数据
export const selectTranslationCache = (state: { translationCache: TranslationCacheState }) => 
  state.translationCache.cache

export const selectTranslationCacheItem = (originalText: string) => 
  (state: { translationCache: TranslationCacheState }): TranslationCacheItem | null => {
    const cacheKey = originalText.trim()
    const cacheItem = state.translationCache.cache[cacheKey]
    
    if (!cacheItem) {
      return null
    }
    
    const now = Date.now()
    // 检查是否过期
    if (now - cacheItem.timestamp > state.translationCache.cacheExpireTime) {
      return null // 过期返回null
    }
    
    return cacheItem
  }

export const selectCacheStats = (state: { translationCache: TranslationCacheState }) => {
  const cache = state.translationCache.cache
  const totalItems = Object.keys(cache).length
  const now = Date.now()
  const expireTime = state.translationCache.cacheExpireTime
  
  let validItems = 0
  let expiredItems = 0
  
  for (const item of Object.values(cache)) {
    if (now - item.timestamp > expireTime) {
      expiredItems++
    } else {
      validItems++
    }
  }
  
  return {
    totalItems,
    validItems,
    expiredItems,
    maxCacheSize: state.translationCache.maxCacheSize,
    cacheExpireTime: state.translationCache.cacheExpireTime
  }
}
