/**
 * 翻译缓存管理工具
 * 使用Chrome Storage API实现翻译结果的缓存功能
 * 专为content script环境设计，提供高性能的翻译结果缓存
 *
 * 特性：
 * - 自动过期清理
 * - LRU缓存策略
 * - 大小写不敏感匹配
 * - 异步操作支持
 * - 错误恢复机制
 */

// 翻译缓存项的类型定义
export interface TranslationCacheItem {
  /** 原文文本 */
  originalText: string
  /** 翻译结果 */
  translatedText: string
  /** 缓存创建时间戳 */
  timestamp: number
  /** 最后访问时间戳（用于LRU策略） */
  lastAccessed: number
}

// 缓存配置接口
export interface CacheConfig {
  /** 最大缓存条目数 */
  maxCacheSize: number
  /** 缓存过期时间（毫秒） */
  cacheExpireTime: number
  /** Chrome Storage存储键名 */
  storageKey: string
  /** 是否启用调试日志 */
  enableDebugLog: boolean
}

// 缓存统计信息接口
export interface CacheStats {
  /** 总缓存条目数 */
  totalItems: number
  /** 有效缓存条目数 */
  validItems: number
  /** 过期缓存条目数 */
  expiredItems: number
  /** 最大缓存大小限制 */
  maxCacheSize: number
  /** 缓存过期时间（毫秒） */
  cacheExpireTime: number
  /** 缓存命中率（如果有统计） */
  hitRate?: number
}

// 默认配置
const DEFAULT_CONFIG: CacheConfig = {
  maxCacheSize: 1000,                    // 最多缓存1000条翻译结果
  cacheExpireTime: 24 * 60 * 60 * 1000,  // 24小时过期
  storageKey: 'ai_extension_translation_cache', // 使用更具体的键名避免冲突
  enableDebugLog: process.env.NODE_ENV === 'development' // 开发环境启用调试日志
}

/**
 * 翻译缓存管理器
 * 提供高性能的翻译结果缓存功能
 */
export class TranslationCacheManager {
  private config: CacheConfig
  private cache: Record<string, TranslationCacheItem> = {}
  private isInitialized = false
  private hitCount = 0
  private totalRequests = 0

  constructor(config?: Partial<CacheConfig>) {
    this.config = { ...DEFAULT_CONFIG, ...config }
  }

  /**
   * 记录调试日志
   */
  private log(message: string, ...args: any[]): void {
    if (this.config.enableDebugLog) {
      console.log(`[TranslationCache] ${message}`, ...args)
    }
  }

  /**
   * 记录错误日志
   */
  private logError(message: string, error?: any): void {
    console.error(`[TranslationCache] ${message}`, error)
  }

  /**
   * 初始化缓存管理器
   * 从Chrome Storage中加载现有缓存
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    try {
      const result = await chrome.storage.local.get([this.config.storageKey])
      const storedCache = result[this.config.storageKey]

      if (storedCache && typeof storedCache === 'object') {
        this.cache = storedCache
        this.log('已加载缓存，条目数:', Object.keys(this.cache).length)

        // 清理过期缓存
        await this.clearExpiredCache()
      } else {
        this.cache = {}
        this.log('初始化空缓存')
      }

      this.isInitialized = true
    } catch (error) {
      this.logError('初始化失败:', error)
      this.cache = {}
      this.isInitialized = true
    }
  }

  /**
   * 生成缓存键
   * 对原文进行简单的标准化处理
   */
  private generateCacheKey(originalText: string): string {
    return originalText.trim().toLowerCase()
  }

  /**
   * 检查缓存项是否过期
   */
  private isExpired(item: TranslationCacheItem): boolean {
    const now = Date.now()
    return (now - item.timestamp) > this.config.cacheExpireTime
  }

  /**
   * 保存缓存到Chrome Storage
   */
  private async saveToStorage(): Promise<void> {
    try {
      await chrome.storage.local.set({
        [this.config.storageKey]: this.cache
      })
    } catch (error) {
      this.logError('保存缓存失败:', error)
    }
  }

  /**
   * 获取翻译缓存
   * @param originalText 原文文本
   * @returns 翻译结果，如果不存在或已过期则返回null
   */
  async getTranslation(originalText: string): Promise<string | null> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    this.totalRequests++
    const cacheKey = this.generateCacheKey(originalText)
    const cacheItem = this.cache[cacheKey]

    if (!cacheItem) {
      this.log('缓存未命中:', originalText.substring(0, 50) + '...')
      return null
    }

    // 检查是否过期
    if (this.isExpired(cacheItem)) {
      // 删除过期缓存
      delete this.cache[cacheKey]
      await this.saveToStorage()
      this.log('缓存已过期，已删除:', originalText.substring(0, 50) + '...')
      return null
    }

    // 更新最后访问时间（LRU策略）
    cacheItem.lastAccessed = Date.now()
    this.hitCount++

    this.log('缓存命中:', originalText.substring(0, 50) + '...')
    return cacheItem.translatedText
  }

  /**
   * 存储翻译结果到缓存
   * @param originalText 原文文本
   * @param translatedText 翻译结果
   */
  async setTranslation(originalText: string, translatedText: string): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    const cacheKey = this.generateCacheKey(originalText)
    const now = Date.now()

    // 如果缓存已满，使用LRU策略删除最久未访问的条目
    const cacheKeys = Object.keys(this.cache)
    if (cacheKeys.length >= this.config.maxCacheSize) {
      let lruKey = cacheKeys[0]
      let lruTime = this.cache[lruKey].lastAccessed

      for (const key of cacheKeys) {
        if (this.cache[key].lastAccessed < lruTime) {
          lruTime = this.cache[key].lastAccessed
          lruKey = key
        }
      }

      delete this.cache[lruKey]
      this.log('缓存已满，删除最久未访问的条目:', this.cache[lruKey]?.originalText?.substring(0, 30) + '...')
    }

    // 存储新的翻译缓存
    this.cache[cacheKey] = {
      originalText,
      translatedText,
      timestamp: now,
      lastAccessed: now
    }

    this.log('存储缓存:', originalText.substring(0, 50) + '...')
    await this.saveToStorage()
  }

  /**
   * 删除指定的翻译缓存
   * @param originalText 原文文本
   */
  async removeTranslation(originalText: string): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    const cacheKey = this.generateCacheKey(originalText)
    if (this.cache[cacheKey]) {
      delete this.cache[cacheKey]
      await this.saveToStorage()
      this.log('删除缓存:', originalText.substring(0, 50) + '...')
    }
  }

  /**
   * 清除所有翻译缓存
   */
  async clearAllCache(): Promise<void> {
    this.cache = {}
    this.hitCount = 0
    this.totalRequests = 0
    await this.saveToStorage()
    this.log('已清除所有缓存')
  }

  /**
   * 清除过期的缓存条目
   */
  async clearExpiredCache(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    const expiredKeys: string[] = []

    // 找出所有过期的key
    for (const [key, item] of Object.entries(this.cache)) {
      if (this.isExpired(item)) {
        expiredKeys.push(key)
      }
    }

    // 删除过期的条目
    expiredKeys.forEach(key => {
      delete this.cache[key]
    })

    if (expiredKeys.length > 0) {
      await this.saveToStorage()
      this.log('清除过期缓存条目数:', expiredKeys.length)
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): CacheStats {
    const totalItems = Object.keys(this.cache).length
    let validItems = 0
    let expiredItems = 0

    for (const item of Object.values(this.cache)) {
      if (this.isExpired(item)) {
        expiredItems++
      } else {
        validItems++
      }
    }

    const hitRate = this.totalRequests > 0 ? (this.hitCount / this.totalRequests) * 100 : 0

    return {
      totalItems,
      validItems,
      expiredItems,
      maxCacheSize: this.config.maxCacheSize,
      cacheExpireTime: this.config.cacheExpireTime,
      hitRate: Math.round(hitRate * 100) / 100 // 保留两位小数
    }
  }

  /**
   * 更新缓存配置
   */
  /**
   * 更新缓存配置
   */
  updateConfig(newConfig: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...newConfig }
    this.log('配置已更新:', this.config)
  }
}

// 创建全局缓存管理器实例
export const translationCacheManager = new TranslationCacheManager()

// 导出便捷函数
export const getTranslationFromCache = (originalText: string) =>
  translationCacheManager.getTranslation(originalText)

export const setTranslationToCache = (originalText: string, translatedText: string) =>
  translationCacheManager.setTranslation(originalText, translatedText)

export const clearTranslationCache = () =>
  translationCacheManager.clearAllCache()
