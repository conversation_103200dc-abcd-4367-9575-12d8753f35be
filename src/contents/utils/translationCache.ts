/**
 * 翻译缓存管理工具
 * 使用Chrome Storage API实现翻译结果的缓存功能
 * 适用于content script环境
 */

// 翻译缓存项的类型定义
export interface TranslationCacheItem {
  originalText: string    // 原文文本
  translatedText: string  // 翻译结果
  timestamp: number       // 缓存时间戳
}

// 缓存配置
export interface CacheConfig {
  maxCacheSize: number      // 最大缓存条目数
  cacheExpireTime: number   // 缓存过期时间（毫秒）
  storageKey: string        // 存储键名
}

// 默认配置
const DEFAULT_CONFIG: CacheConfig = {
  maxCacheSize: 1000,                    // 最多缓存1000条翻译结果
  cacheExpireTime: 24 * 60 * 60 * 1000,  // 24小时过期
  storageKey: 'translation_cache'         // 存储键名
}

/**
 * 翻译缓存管理器
 */
export class TranslationCacheManager {
  private config: CacheConfig
  private cache: Record<string, TranslationCacheItem> = {}
  private isInitialized = false

  constructor(config?: Partial<CacheConfig>) {
    this.config = { ...DEFAULT_CONFIG, ...config }
  }

  /**
   * 初始化缓存管理器
   * 从Chrome Storage中加载现有缓存
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    try {
      const result = await chrome.storage.local.get([this.config.storageKey])
      const storedCache = result[this.config.storageKey]
      
      if (storedCache && typeof storedCache === 'object') {
        this.cache = storedCache
        console.log('TranslationCache: 已加载缓存，条目数:', Object.keys(this.cache).length)
        
        // 清理过期缓存
        await this.clearExpiredCache()
      } else {
        this.cache = {}
        console.log('TranslationCache: 初始化空缓存')
      }
      
      this.isInitialized = true
    } catch (error) {
      console.error('TranslationCache: 初始化失败:', error)
      this.cache = {}
      this.isInitialized = true
    }
  }

  /**
   * 生成缓存键
   * 对原文进行简单的标准化处理
   */
  private generateCacheKey(originalText: string): string {
    return originalText.trim().toLowerCase()
  }

  /**
   * 检查缓存项是否过期
   */
  private isExpired(item: TranslationCacheItem): boolean {
    const now = Date.now()
    return (now - item.timestamp) > this.config.cacheExpireTime
  }

  /**
   * 保存缓存到Chrome Storage
   */
  private async saveToStorage(): Promise<void> {
    try {
      await chrome.storage.local.set({
        [this.config.storageKey]: this.cache
      })
    } catch (error) {
      console.error('TranslationCache: 保存缓存失败:', error)
    }
  }

  /**
   * 获取翻译缓存
   * @param originalText 原文文本
   * @returns 翻译结果，如果不存在或已过期则返回null
   */
  async getTranslation(originalText: string): Promise<string | null> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    const cacheKey = this.generateCacheKey(originalText)
    const cacheItem = this.cache[cacheKey]

    if (!cacheItem) {
      return null
    }

    // 检查是否过期
    if (this.isExpired(cacheItem)) {
      // 删除过期缓存
      delete this.cache[cacheKey]
      await this.saveToStorage()
      return null
    }

    console.log('TranslationCache: 命中缓存:', originalText.substring(0, 50) + '...')
    return cacheItem.translatedText
  }

  /**
   * 存储翻译结果到缓存
   * @param originalText 原文文本
   * @param translatedText 翻译结果
   */
  async setTranslation(originalText: string, translatedText: string): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    const cacheKey = this.generateCacheKey(originalText)
    const now = Date.now()

    // 如果缓存已满，删除最旧的条目
    const cacheKeys = Object.keys(this.cache)
    if (cacheKeys.length >= this.config.maxCacheSize) {
      let oldestKey = cacheKeys[0]
      let oldestTime = this.cache[oldestKey].timestamp

      for (const key of cacheKeys) {
        if (this.cache[key].timestamp < oldestTime) {
          oldestTime = this.cache[key].timestamp
          oldestKey = key
        }
      }

      delete this.cache[oldestKey]
      console.log('TranslationCache: 删除最旧缓存条目:', oldestKey)
    }

    // 存储新的翻译缓存
    this.cache[cacheKey] = {
      originalText,
      translatedText,
      timestamp: now
    }

    console.log('TranslationCache: 存储缓存:', originalText.substring(0, 50) + '...')
    await this.saveToStorage()
  }

  /**
   * 删除指定的翻译缓存
   * @param originalText 原文文本
   */
  async removeTranslation(originalText: string): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    const cacheKey = this.generateCacheKey(originalText)
    if (this.cache[cacheKey]) {
      delete this.cache[cacheKey]
      await this.saveToStorage()
      console.log('TranslationCache: 删除缓存:', originalText.substring(0, 50) + '...')
    }
  }

  /**
   * 清除所有翻译缓存
   */
  async clearAllCache(): Promise<void> {
    this.cache = {}
    await this.saveToStorage()
    console.log('TranslationCache: 已清除所有缓存')
  }

  /**
   * 清除过期的缓存条目
   */
  async clearExpiredCache(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    const expiredKeys: string[] = []
    
    // 找出所有过期的key
    for (const [key, item] of Object.entries(this.cache)) {
      if (this.isExpired(item)) {
        expiredKeys.push(key)
      }
    }

    // 删除过期的条目
    expiredKeys.forEach(key => {
      delete this.cache[key]
    })

    if (expiredKeys.length > 0) {
      await this.saveToStorage()
      console.log('TranslationCache: 清除过期缓存条目数:', expiredKeys.length)
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): {
    totalItems: number
    validItems: number
    expiredItems: number
    maxCacheSize: number
    cacheExpireTime: number
  } {
    const totalItems = Object.keys(this.cache).length
    const now = Date.now()
    let validItems = 0
    let expiredItems = 0

    for (const item of Object.values(this.cache)) {
      if (this.isExpired(item)) {
        expiredItems++
      } else {
        validItems++
      }
    }

    return {
      totalItems,
      validItems,
      expiredItems,
      maxCacheSize: this.config.maxCacheSize,
      cacheExpireTime: this.config.cacheExpireTime
    }
  }

  /**
   * 更新缓存配置
   */
  updateConfig(newConfig: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...newConfig }
    console.log('TranslationCache: 配置已更新:', this.config)
  }
}

// 创建全局缓存管理器实例
export const translationCacheManager = new TranslationCacheManager()

// 导出便捷函数
export const getTranslationFromCache = (originalText: string) => 
  translationCacheManager.getTranslation(originalText)

export const setTranslationToCache = (originalText: string, translatedText: string) => 
  translationCacheManager.setTranslation(originalText, translatedText)

export const clearTranslationCache = () => 
  translationCacheManager.clearAllCache()
