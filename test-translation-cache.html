<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Translation Cache Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .test-button {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #005a87;
        }
        .console-output {
            background-color: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .text-content {
            margin: 10px 0;
            padding: 10px;
            background-color: white;
            border-left: 4px solid #007cba;
        }
    </style>
</head>
<body>
    <h1>Translation Cache Test Page</h1>
    <p>This page is designed to test the translation cache functionality of the AI Chrome Extension.</p>

    <div class="test-section">
        <h2>Test Instructions</h2>
        <ol>
            <li>Make sure the AI Chrome Extension is installed and active</li>
            <li>Open the browser console (F12) to see test results</li>
            <li>Click the test buttons below to run cache tests</li>
            <li>Try translating the same text multiple times to test cache hits</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Manual Cache Tests</h2>
        <button class="test-button" onclick="runBasicCacheTest()">Run Basic Cache Test</button>
        <button class="test-button" onclick="runExpirationTest()">Run Expiration Test</button>
        <button class="test-button" onclick="clearAllCache()">Clear All Cache</button>
        <button class="test-button" onclick="showCacheStats()">Show Cache Stats</button>
    </div>

    <div class="test-section">
        <h2>Sample Text for Translation Testing</h2>
        <p>Select and translate the following text multiple times to test cache functionality:</p>
        
        <div class="text-content">
            <p>Hello, this is a test message for translation cache functionality. The cache should store the translation result and return it immediately on subsequent requests for the same text.</p>
        </div>

        <div class="text-content">
            <p>Good morning! How are you today? I hope you're having a wonderful day.</p>
        </div>

        <div class="text-content">
            <p>The quick brown fox jumps over the lazy dog. This is a common English pangram used for testing.</p>
        </div>

        <div class="text-content">
            <p>Artificial Intelligence and Machine Learning are transforming the way we interact with technology.</p>
        </div>

        <div class="text-content">
            <p>Translation cache improves user experience by avoiding redundant API calls and providing instant results for previously translated content.</p>
        </div>
    </div>

    <div class="test-section">
        <h2>Console Output</h2>
        <div id="console-output" class="console-output">
            Console output will appear here...
        </div>
    </div>

    <script>
        // Capture console output and display it on the page
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function addToConsoleOutput(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '[ERROR]' : type === 'warn' ? '[WARN]' : '[LOG]';
            consoleOutput.textContent += `${timestamp} ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsoleOutput(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsoleOutput(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsoleOutput(args.join(' '), 'warn');
        };

        // Test functions
        function runBasicCacheTest() {
            console.log('=== Running Basic Cache Test ===');
            if (window.testTranslationCache) {
                window.testTranslationCache();
            } else {
                console.error('testTranslationCache function not available. Make sure the extension is loaded.');
            }
        }

        function runExpirationTest() {
            console.log('=== Running Expiration Test ===');
            if (window.testCacheExpiration) {
                window.testCacheExpiration();
            } else {
                console.error('testCacheExpiration function not available. Make sure the extension is loaded.');
            }
        }

        function clearAllCache() {
            console.log('=== Clearing All Cache ===');
            if (window.clearTranslationCache) {
                window.clearTranslationCache().then(() => {
                    console.log('All translation cache cleared successfully');
                }).catch(error => {
                    console.error('Failed to clear cache:', error);
                });
            } else {
                console.error('clearTranslationCache function not available. Make sure the extension is loaded.');
            }
        }

        function showCacheStats() {
            console.log('=== Cache Statistics ===');
            // This would need to be implemented in the cache manager
            console.log('Cache stats functionality would be implemented here');
        }

        // Initialize page
        window.addEventListener('load', function() {
            console.log('Translation Cache Test Page loaded');
            console.log('Extension functions available:', {
                testTranslationCache: typeof window.testTranslationCache,
                testCacheExpiration: typeof window.testCacheExpiration,
                clearTranslationCache: typeof window.clearTranslationCache
            });
        });

        // Check for extension every 2 seconds
        let checkCount = 0;
        const checkInterval = setInterval(() => {
            checkCount++;
            if (window.testTranslationCache) {
                console.log('Extension detected! Cache test functions are available.');
                clearInterval(checkInterval);
            } else if (checkCount > 10) {
                console.warn('Extension not detected after 20 seconds. Please make sure the AI Chrome Extension is installed and active.');
                clearInterval(checkInterval);
            }
        }, 2000);
    </script>
</body>
</html>
